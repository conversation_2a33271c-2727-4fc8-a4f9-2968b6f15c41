{"version": 3, "file": "multiSignatureAptos.js", "sourceRoot": "", "sources": ["../../../../../../src/signing/chains/multiSignatureAptos.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,OAAO,EAAE,eAAe,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAE9D,MAAM,CAAC,OAAO,OAAO,yBAAyB;IACpC,UAAU,CAAS;IAClB,WAAW,GAAW,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC;IACvE,eAAe,GAAW,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC;IAC3E,aAAa,GAAW,eAAe,CAAC,UAAU,CAAC;IAElD,iBAAiB,CAA+E;IAEhG,QAAQ,CAAM;IAExB,YAAY,SAAiB,EAAE,iBAA+F;QAC5H,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;IAC7C,CAAC;IAED,IAAW,SAAS;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,OAAmB;QAC5B,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAC3E,iGAAiG;QACjG,0CAA0C;QAC1C,MAAM,cAAc,GAAG,GAAG,CAAC;QAC3B,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAE5C,oCAAoC;QACpC,MAAM,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;QAE9B,IAAI,CAAC,OAAO,CAAC,CAAC,GAAW,EAAE,EAAE;YAC3B,IAAI,GAAG,IAAI,EAAE,EAAE;gBACb,MAAM,IAAI,KAAK,CAAC,qBAAqB,GAAG,GAAG,CAAC,CAAC;aAC9C;YAED,IAAI,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACxB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;aAC9C;YAED,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAErB,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YAEvC,IAAI,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;YAE9B,IAAI,IAAI,cAAc,IAAI,GAAG,GAAG,CAAC,CAAC;YAElC,MAAM,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACrD,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;YAC3B,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;gBACpB,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;aAC7C;SACF;QAED,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;QAChD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,OAAmB,EAAE,SAAqB;QACxE,MAAM,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC;QACzE,MAAM,SAAS,GAAG,eAAe,GAAG,CAAC,CAAC;QACtC,MAAM,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QACjD,MAAM,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAEjD,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;YAC3B,eAAe;YACf,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACjC,MAAM,SAAS,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;YACjC,MAAM,WAAW,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC;YACvE,IAAI,WAAW,EAAE;gBACf,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;gBACzD,MAAM,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC9C,IAAI,CAAC,CAAC,MAAM,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;oBAAE,QAAQ,GAAG,IAAI,CAAC;aACzG;SACF;QACD,OAAO,CAAC,QAAQ,CAAC;IACnB,CAAC;CACF"}