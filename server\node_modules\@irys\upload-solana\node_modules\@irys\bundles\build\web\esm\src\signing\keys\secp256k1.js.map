{"version": 3, "file": "secp256k1.js", "sourceRoot": "", "sources": ["../../../../../../src/signing/keys/secp256k1.ts"], "names": [], "mappings": "AACA,OAAO,SAAS,MAAM,WAAW,CAAC;AAClC,OAAO,SAAS,MAAM,WAAW,CAAC;AAClC,OAAO,EAAE,eAAe,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAC9D,OAAO,SAAS,MAAM,cAAc,CAAC;AAErC,MAAM,CAAC,OAAO,OAAO,SAAS;IAMN;IALb,WAAW,GAAW,UAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC;IACrE,eAAe,GAAW,UAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC;IACzE,aAAa,GAAoB,eAAe,CAAC,QAAQ,CAAC;IACnD,EAAE,CAAS;IAE3B,YAAsB,IAAY,EAAE,EAAU;QAAxB,SAAI,GAAJ,IAAI,CAAQ;QAChC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED,IAAW,SAAS;QAClB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACpD,CAAC;IAED,IAAW,GAAG;QACZ,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACvC,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAmB,EAAE,OAAmB,EAAE,SAAqB;QACjF,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,IAAI,OAAO,EAAE,KAAK,QAAQ;YAAE,CAAC,GAAG,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACvD,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAI;YACF,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAW,CAAC,CAAC;YAC1F,oCAAoC;SACrC;QAAC,OAAO,CAAC,EAAE,GAAE;QACd,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,OAAmB;QAC5B,OAAO,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC;IAC/F,CAAC;CACF"}