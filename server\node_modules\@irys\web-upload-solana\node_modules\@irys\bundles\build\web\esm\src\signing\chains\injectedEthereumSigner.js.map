{"version": 3, "file": "injectedEthereumSigner.js", "sourceRoot": "", "sources": ["../../../../../../src/signing/chains/injectedEthereumSigner.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;AAE9D,OAAO,EAAE,eAAe,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAE9D,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AAChD,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAC7D,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AAQtD,MAAM,OAAO,sBAAsB;IACjC,mCAAmC;IACzB,MAAM,CAAsC;IAC/C,SAAS,CAAS;IAChB,WAAW,GAAW,UAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC;IACrE,eAAe,GAAW,UAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC;IACzE,aAAa,GAAoB,eAAe,CAAC,QAAQ,CAAC;IAEnE,YAAY,QAA+C;QACzD,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,MAAM,OAAO,GAAG,gDAAgD,CAAC;QACjE,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACzD,MAAM,IAAI,GAAG,MAAM,WAAW,CAAC,OAAO,CAAC,CAAC;QACxC,MAAM,YAAY,GAAG,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,CAAC;QACjE,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;IACvD,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,OAAmB;QAC5B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;SAC3B;QACD,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACnD,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED,MAAM,CAAC,MAAM,CAAC,EAAU,EAAE,OAAmB,EAAE,SAAqB;QAClE,MAAM,OAAO,GAAG,cAAc,CAAC,EAAE,CAAC,CAAC;QACnC,OAAO,aAAa,CAAC,OAAO,EAAE,SAAS,CAAC,KAAK,OAAO,CAAC;IACvD,CAAC;CACF;AACD,eAAe,sBAAsB,CAAC"}