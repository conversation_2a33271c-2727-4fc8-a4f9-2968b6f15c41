/*
 * [hi-base32]{@link https://github.com/emn178/hi-base32}
 *
 * @version 0.5.0
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON> [<EMAIL>]
 * @copyright Chen, <PERSON><PERSON><PERSON><PERSON> 2015-2018
 * @license MIT
 */

/*
 * [js-sha512]{@link https://github.com/emn178/js-sha512}
 *
 * @version 0.8.0
 * <AUTHOR> <PERSON> [<EMAIL>]
 * @copyright <PERSON>, <PERSON><PERSON><PERSON><PERSON> 2014-2018
 * @license MIT
 */

/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> <https://feross.org>
 * @license  MIT
 */

/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */

/**
 * [js-sha256]{@link https://github.com/emn178/js-sha256}
 *
 * @version 0.9.0
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON> [<EMAIL>]
 * @copyright <PERSON>, <PERSON><PERSON><PERSON><PERSON> 2014-2017
 * @license MIT
 */

/**
 * [js-sha3]{@link https://github.com/emn178/js-sha3}
 *
 * @version 0.8.0
 * <AUTHOR> <PERSON>-<PERSON><PERSON> [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2015-2018
 * @license MIT
 */
