{"version": 3, "file": "injectedStarknetSigner.js", "sourceRoot": "", "sources": ["../../../../../../src/signing/chains/injectedStarknetSigner.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,UAAU,CAAC;AAEvD,OAAO,EAAE,YAAY,EAAE,6BAA6B,EAAE,MAAM,kBAAkB,CAAC;AAC/E,OAAO,EAAE,eAAe,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAC9D,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAEhD,MAAM,CAAC,OAAO,OAAO,sBAAsB;IAClC,aAAa,CAAgB;IAC7B,SAAS,CAAS;IAClB,QAAQ,CAAc;IACtB,OAAO,CAAS;IAChB,iBAAiB,CAAS;IACxB,WAAW,GAAW,UAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC;IACrE,eAAe,GAAW,UAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC;IACzE,aAAa,GAAW,eAAe,CAAC,QAAQ,CAAC;IAE1D,YAAY,QAAqB,EAAE,aAA4B,EAAE,iBAAyB;QACxF,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;IAC7C,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,MAAc;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;QAE3C,iCAAiC;QACjC,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC5F,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAChG,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEpE,gCAAgC;QAChC,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,aAAa,EAAE,iBAAiB,CAAC,CAAC,CAAC;QAEjF,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,OAAmB,EAAE,KAAW;QACzC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW;YAAE,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QAEzG,sCAAsC;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC,CAAC;QAC/E,MAAM,IAAI,GAAc,YAAY,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACnD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7D,kFAAkF;QAClF,yEAAyE;QACzE,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,MAAM,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QACjE,MAAM,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QAEjE,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;QACtD,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;QACtD,MAAM,cAAc,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;QAEzG,yBAAyB;QACzB,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;QACrF,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACnB,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAClC,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QAE1D,wCAAwC;QACxC,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE;YAAE,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QAEhF,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAc,EAAE,OAAmB,EAAE,SAAqB,EAAE,KAAW;QACzF,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,MAAM,OAAO,GAAG,EAAE,CAAC;QAEnB,0CAA0C;QAC1C,MAAM,cAAc,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3C,MAAM,eAAe,GAAG,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAEjF,kCAAkC;QAClC,MAAM,qBAAqB,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC;QACjE,MAAM,eAAe,GAAG,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAElF,4BAA4B;QAC5B,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC;QAEvE,sCAAsC;QACtC,MAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC,CAAC;QAC/E,MAAM,IAAI,GAAc,YAAY,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;QAC3D,MAAM,OAAO,GAAG,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;QAChE,MAAM,gBAAgB,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAEjD,SAAS;QACT,OAAO,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;IACrE,CAAC;CACF"}