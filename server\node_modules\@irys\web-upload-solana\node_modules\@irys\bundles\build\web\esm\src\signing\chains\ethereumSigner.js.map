{"version": 3, "file": "ethereumSigner.js", "sourceRoot": "", "sources": ["../../../../../../src/signing/chains/ethereumSigner.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,MAAM,mBAAmB,CAAC;AAC1C,OAAO,SAAS,MAAM,WAAW,CAAC;AAClC,OAAO,SAAS,MAAM,WAAW,CAAC;AAClC,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AAChD,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAElD,MAAM,CAAC,OAAO,OAAO,cAAe,SAAQ,SAAS;IACnD,IAAW,SAAS;QAClB,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IACrC,CAAC;IAED,YAAY,GAAW;QACrB,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC;YAAE,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAClC,MAAM,GAAG,GAAG,SAAS,CAAC,eAAe,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAChD,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,OAAmB;QAC5B,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,OAAO,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAQ,CAAC;QACtF,0DAA0D;QAC1D,2IAA2I;IAC7I,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAmB,EAAE,OAAmB,EAAE,SAAqB;QACjF,mDAAmD;QACnD,qEAAqE;QACrE,OAAO,SAAS,CAAC,WAAW,CAC1B,SAAS,CAAC,MAAM,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAC5D,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,EAC9B,OAAO,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CACrD,CAAC;IACJ,CAAC;CACF"}