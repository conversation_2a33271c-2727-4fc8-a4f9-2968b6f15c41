import { hashMessage } from "@ethersproject/hash";
import { recoverPublicKey } from "@ethersproject/signing-key";
import { SignatureConfig, SIG_CONFIG } from "../../constants.js";
import { arrayify } from "@ethersproject/bytes";
import { computeAddress } from "@ethersproject/transactions";
import { verifyMessage } from "@ethersproject/wallet";
export class InjectedEthereumSigner {
    // protected signer: JsonRpcSigner;
    signer;
    publicKey;
    ownerLength = SIG_CONFIG[SignatureConfig.ETHEREUM].pubLength;
    signatureLength = SIG_CONFIG[SignatureConfig.ETHEREUM].sigLength;
    signatureType = SignatureConfig.ETHEREUM;
    constructor(provider) {
        this.signer = provider.getSigner();
    }
    async setPublicKey() {
        const address = "sign this message to connect to Bundlr.Network";
        const signedMsg = await this.signer.signMessage(address);
        const hash = await hashMessage(address);
        const recoveredKey = recoverPublicKey(arrayify(hash), signedMsg);
        this.publicKey = Buffer.from(arrayify(recoveredKey));
    }
    async sign(message) {
        if (!this.publicKey) {
            await this.setPublicKey();
        }
        const sig = await this.signer.signMessage(message);
        return Buffer.from(sig.slice(2), "hex");
    }
    static verify(pk, message, signature) {
        const address = computeAddress(pk);
        return verifyMessage(message, signature) === address;
    }
}
export default InjectedEthereumSigner;
//# sourceMappingURL=injectedEthereumSigner.js.map