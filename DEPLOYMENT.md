# Deployment Guide

## Production Deployment

### Environment Variables

For production deployment, configure these environment variables:

```bash
# Solana Network
VITE_SOLANA_NETWORK=mainnet-beta
VITE_SOLANA_RPC_URL=https://your-rpc-endpoint.com

# IPFS Configuration (Required for production)
VITE_PINATA_API_KEY=your_pinata_api_key
VITE_PINATA_SECRET_KEY=your_pinata_secret_key

# Backend Configuration
PORT=3001
NODE_ENV=production
VITE_API_BASE_URL=https://your-api-domain.com
```

### Security Considerations

1. **Server Wallet Management**
   - The current implementation uses temporary keypairs for demo purposes
   - In production, implement one of these approaches:
     - Client-side transaction signing (recommended)
     - Secure server wallet with proper key management
     - Hardware Security Module (HSM) integration

2. **API Security**
   - Implement rate limiting
   - Add authentication/authorization
   - Use HTTPS in production
   - Validate all inputs server-side

3. **CORS Configuration**
   - Update CORS origins for production domains
   - Remove development URLs

### Frontend Deployment

#### Vercel (Recommended)
```bash
npm run build
# Deploy dist/ folder to Vercel
```

#### Netlify
```bash
npm run build
# Deploy dist/ folder to Netlify
```

#### Traditional Hosting
```bash
npm run build
# Upload dist/ folder to your web server
```

### Backend Deployment

#### Railway
1. Connect your GitHub repository
2. Set environment variables
3. Deploy automatically

#### Heroku
```bash
# Add Heroku remote
heroku create your-app-name

# Set environment variables
heroku config:set NODE_ENV=production
heroku config:set VITE_SOLANA_NETWORK=mainnet-beta

# Deploy
git push heroku main
```

#### DigitalOcean App Platform
1. Connect repository
2. Configure build settings
3. Set environment variables
4. Deploy

#### VPS/Dedicated Server
```bash
# Install dependencies
npm install --production

# Start with PM2
npm install -g pm2
pm2 start server/index.js --name solana-token-creator

# Setup reverse proxy with Nginx
# Configure SSL with Let's Encrypt
```

### Database Considerations

For production, consider adding:
- User authentication
- Token creation history
- Analytics tracking
- Rate limiting per user

### Monitoring

Implement monitoring for:
- Server health
- Transaction success rates
- IPFS upload success
- Error tracking
- Performance metrics

### Backup Strategy

- Backup user data (if storing any)
- Monitor IPFS pin status
- Keep transaction logs
- Regular security audits
