{"version": 3, "file": "arconnectSigner.js", "sourceRoot": "", "sources": ["../../../../../../src/signing/chains/arconnectSigner.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAE9D,OAAO,SAAS,MAAM,WAAW,CAAC;AAGlC,OAAO,EAAE,eAAe,EAAE,MAAM,SAAS,CAAC;AAE1C,MAAM,CAAC,OAAO,OAAO,qBAAqB;IAChC,MAAM,CAA0B;IACjC,SAAS,CAAS;IAChB,WAAW,GAAW,UAAU,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC;IACpE,eAAe,GAAW,UAAU,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC;IACxE,aAAa,GAAoB,eAAe,CAAC,OAAO,CAAC;IACxD,OAAO,CAAU;IAC3B,YAAY,mBAA4C,EAAE,OAAgB;QACxE,IAAI,CAAC,MAAM,GAAG,mBAAmB,CAAC;QAClC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;QACvD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,OAAmB;QAC5B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;SAC3B;QAED,MAAM,SAAS,GAAG;YAChB,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,EAAE;SACf,CAAC;QAEF,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAClE,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpE,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,OAAmB,EAAE,SAAqB;QACxE,OAAO,MAAM,eAAe,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;IAChE,CAAC;CACF"}