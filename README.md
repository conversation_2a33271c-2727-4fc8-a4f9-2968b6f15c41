# Solana SPL Token Creator

A comprehensive web application for creating SPL tokens on the Solana blockchain with metadata and IPFS storage.

## Features

- 🔗 **Phantom Wallet Integration** - Connect your wallet to use your SOL for transactions
- 🪙 **SPL Token Creation** - Create custom tokens with metadata
- 🖼️ **Image Upload & Processing** - Upload and optimize token logos
- 📦 **IPFS Storage** - Decentralized storage for images and metadata via Pinata
- 🔒 **Authority Management** - Option to revoke mint and freeze authorities
- 📱 **Responsive Design** - Works on desktop and mobile
- ⚡ **Real-time Status** - Live transaction progress and confirmations
- 🌐 **Multi-Network Support** - Devnet, Testnet, and Mainnet

## Tech Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **Solana Wallet Adapter** for wallet integration
- **React Hot Toast** for notifications
- **Lucide React** for icons

### Backend
- **Node.js** with Express
- **Metaplex Foundation** libraries for token creation
- **Sharp** for image processing
- **<PERSON>nata** for IPFS uploads
- **Multer** for file uploads

## Prerequisites

- Node.js 18+ 
- npm or yarn
- Phantom wallet browser extension
- SOL tokens for transaction fees (devnet SOL for testing)

## Quick Start

### 1. Install Dependencies

```bash
# Install root dependencies
npm install

# Install server dependencies
cd server
npm install
cd ..
```

### 2. Environment Setup

Copy the `.env` file and configure your settings:

```bash
# Solana Network (devnet recommended for testing)
VITE_SOLANA_NETWORK=devnet

# Optional: Custom RPC URL for better performance
# VITE_SOLANA_RPC_URL=https://devnet.helius-rpc.com/?api-key=YOUR_API_KEY

# IPFS Configuration (optional - will use mock service if not provided)
# VITE_PINATA_API_KEY=your_pinata_api_key
# VITE_PINATA_SECRET_KEY=your_pinata_secret_key

# Backend Configuration
PORT=3001
NODE_ENV=development
VITE_API_BASE_URL=http://localhost:3001
```

### 3. Get Devnet SOL

For testing on devnet:
1. Visit [Solana Faucet](https://faucet.solana.com/)
2. Enter your wallet address
3. Request devnet SOL

### 4. Start the Application

```bash
# Start both frontend and backend
npm run dev:full

# Or start them separately:
# Frontend (http://localhost:5173)
npm run dev

# Backend (http://localhost:3001)
npm run dev:server
```

## Usage

### Creating a Token

1. **Connect Wallet**: Click "Connect Wallet" and select Phantom
2. **Fill Token Details**:
   - Token Name (e.g., "My Awesome Token")
   - Symbol (e.g., "MAT")
   - Description
   - Total Supply
   - Decimal Places (9 is standard)
3. **Upload Logo**: Optional PNG/JPG image (max 5MB)
4. **Authority Settings**: Choose whether to revoke mint/freeze authorities
5. **Create Token**: Review estimated cost and confirm

### Token Creation Process

1. **Image Processing**: Uploaded images are resized and optimized
2. **IPFS Upload**: Image and metadata uploaded to decentralized storage
3. **Token Creation**: SPL token created on Solana with metadata
4. **Authority Revocation**: Optional removal of mint/freeze authorities
5. **Confirmation**: Transaction links and token details provided

## Configuration

### Solana Networks

- **Devnet**: For testing (free SOL from faucet)
- **Testnet**: Alternative testing network
- **Mainnet**: Production network (requires real SOL)

### IPFS Storage

The app supports multiple IPFS providers:
- **Pinata** (recommended): Configure API keys in `.env`
- **Mock Service**: Automatic fallback for development

### RPC Endpoints

For better performance, consider using:
- [Helius](https://helius.xyz/) - Free tier available
- [QuickNode](https://quicknode.com/) - Solana RPC provider
- [Alchemy](https://alchemy.com/) - Web3 infrastructure

## API Endpoints

### Backend Server

- `GET /health` - Health check
- `POST /api/create-token` - Create SPL token

### Request Format

```javascript
// FormData with:
{
  name: string,
  symbol: string,
  description: string,
  totalSupply: string,
  decimals: number,
  revokeAuthorities: boolean,
  walletAddress: string,
  image?: File
}
```

## Development

### Project Structure

```
├── src/                    # Frontend React app
│   ├── components/         # React components
│   ├── App.tsx            # Main app component
│   ├── main.tsx           # React entry point
│   └── index.css          # Tailwind styles
├── server/                # Backend Express server
│   ├── handlers/          # Request handlers
│   ├── services/          # Business logic
│   └── index.js           # Server entry point
├── public/                # Static assets
└── package.json           # Dependencies and scripts
```

### Available Scripts

```bash
npm run dev              # Start frontend dev server
npm run build           # Build for production
npm run preview         # Preview production build
npm run server          # Start backend server
npm run dev:server      # Start backend with nodemon
npm run dev:full        # Start both frontend and backend
```

## Troubleshooting

### Common Issues

1. **Wallet Connection Failed**
   - Ensure Phantom wallet is installed
   - Check if wallet is unlocked
   - Try refreshing the page

2. **Insufficient SOL Balance**
   - Get devnet SOL from faucet for testing
   - Ensure wallet has enough SOL for transaction fees

3. **Token Creation Failed**
   - Check Solana network status
   - Verify RPC endpoint is working
   - Check browser console for errors

4. **Image Upload Issues**
   - Ensure image is under 5MB
   - Use supported formats (PNG, JPG, GIF, WebP)
   - Check IPFS service configuration

### Debug Mode

Set `NODE_ENV=development` for detailed error messages and stack traces.

## Security Notes

⚠️ **Important Security Considerations**

1. **Server Wallet**: The current implementation uses a temporary keypair for demo purposes. In production:
   - Implement proper wallet integration where users sign transactions
   - Use secure server wallet management if server-side signing is required
   - Never expose private keys in client-side code

2. **Environment Variables**: Keep sensitive data in `.env` files and never commit them to version control

3. **RPC Endpoints**: Use rate-limited RPC endpoints in production

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For issues and questions:
- Check the troubleshooting section
- Review Solana documentation
- Open an issue on GitHub
